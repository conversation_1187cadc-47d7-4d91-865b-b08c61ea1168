
.screen-capture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.screen-capture-button {
  cursor: pointer;

  svg {
    width: 22px;
    height: 22px;
    transition: color 0.2s ease;
    margin-bottom: 2px !important;
  }

  &:hover svg {
    color: #1890ff !important;
  }

  &.capturing svg {
    color: #1890ff !important;
  }
}

.capture-mode-toggle {
  cursor: pointer;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 10px;
  font-weight: bold;
  color: #fff;
  transition: all 0.2s ease;
  min-width: 20px;
  text-align: center;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #1890ff;
  }

  .mode-indicator {
    display: block;
    line-height: 1;
  }
}